import { computed } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { REG_PHONE } from '@/constants/reg';
import { $t } from '@/locales';
import { fetchSendRegisterSms } from '@/service/api';

export function useCaptcha(type: string = 'register') {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(120);

  const label = computed(() => {
    let text = $t('page.login.codeLogin.getCode');

    const countingLabel = $t('page.login.codeLogin.reGetCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  function isPhoneValid(phone: string) {
    if (phone.trim() === '') {
      window.$message?.error?.($t('form.phone.required'));

      return false;
    }

    if (!REG_PHONE.test(phone)) {
      window.$message?.error?.($t('form.phone.invalid'));

      return false;
    }

    return true;
  }

  async function getCaptcha(phone: string) {
    const valid = isPhoneValid(phone);

    if (!valid || loading.value) {
      return;
    }

    startLoading();

    // request
    try {
      const { data: registerSmsResponse, error } = await fetchSendRegisterSms(phone, type);
      if (error) {
        // API调用失败，错误消息已经通过request拦截器显示了
        console.error('验证码发送失败:', error);
        endLoading();
        return;
      } else {
        console.log("验证码====", registerSmsResponse.code);
        window.$message?.success?.($t('page.login.codeLogin.sendCodeSuccess'));
        start(); // 只有成功时才启动倒计时
      }
    } catch (error) {
      // 捕获异常
      console.error('验证码发送异常:', error);
      endLoading();
      return;
    }

    endLoading();
  }

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptcha
  };
}
