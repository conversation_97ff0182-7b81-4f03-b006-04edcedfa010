import { Api } from '@/typings/api';
import { request } from '../request';

/**
 * @description 登录
 * @param phone 手机号
 * @param password 密码
 * @returns 
 */
export function fetchLogin(phone: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/login',
    method: 'post',
    data: {
      phone,
      password
    }
  });
}

/**
 * @description 发送注册验证码
 * @param phone 手机号
 * @returns 
 */
export function fetchSendRegisterSms(phone: string, type: string) {
  return request<Api.RegisterSms.RegisterSmsResponse>({ 
    url: '/api/v1/send-sms',
    method: 'post',
    data: {
      phone,
      type
    }
  });
}

/**
 * @description 注册
 * @param userName 用户名
 * @param code 短信验证码
 * @param password 密码
 * @param confirmPassword 确认密码
 * @returns
 */
export function fetchRegister(nickname: string, phone: string, sms_code: string, password: string) {
  return request<Api.Register.RegisterResponse>({
    url: '/api/v1/register',
    method: 'post',
    data: {
      nickname,
      phone,
      sms_code,
      password
    }
  });
}

/**
 * @description 重置密码
 * @param phone 手机号
 * @param code 短信验证码
 * @param password 新密码
 * @returns
 */
export function fetchResetPassword(phone: string, sms_code: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/v1/reset-password',
    method: 'post',
    data: {
      phone,
      sms_code,
      password
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/auth/getUserInfo' });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
